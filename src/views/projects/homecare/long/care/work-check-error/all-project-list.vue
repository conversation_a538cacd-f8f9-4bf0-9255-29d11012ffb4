<template>
	<div class="table-container ut-body" :class="{ 'ut-fullscreen': fullscreen }">
		<form-list
			:loading="listLoading"
			:columns="columns"
			:height="height"
			:op-width="160"
			:op-fixed="true"
			:data-list="dataList"
			:select-window="selectWindow"
			:select-single="selectSingle"
			:show-list="showList"
			@select="handleSelect"
			@fetchData="fetchData"
			@fullscreen="onFullscreen"
			@selectRows="onSelectRows"
		>
			<template slot="button">
				<el-button v-if="selectWindow" icon="el-icon-plus" type="primary" :disabled="!selectRows.length" @click="$emit('handleSelect', selectRows)">选择</el-button>
			</template>
			<template #cell="{ row, item }">
				<div v-if="item.prop === 'xxx'"></div>
				<span v-else>{{ row[item.prop] }}</span>
			</template>
		</form-list>
	</div>
</template>
<script>
	import Vue from 'vue'
	import { mapGetters } from 'vuex'
	import FormList from '@/views/components/form-list'

	export default {
		name: 'AllProject',
		components: {
			FormList,
		},
		props: {
			height: {
				type: [Number, String],
				default: () => Vue.prototype.$baseTableHeight(1),
			},
			selectWindow: {
				type: Boolean,
				default: false,
			},
			selectSingle: {
				type: Boolean,
				default: false,
			},
			showList: {
				type: Boolean,
				default: () => false,
			},
			parentData: {
				type: Object,
				default: () => ({}),
			},
		},
		data() {
			return {
				listLoading: true,
				fullscreen: false,
				columns: [
					{
						label: '名称',
						align: 'left',
						width: '300',
						prop: 'name',
						show: true,
					},
					{
						label: '对应社保编号',
						align: 'center',
						width: '160',
						prop: 'govCode',
						show: true,
					},
					{
						label: '服务时长',
						align: 'center',
						width: '90',
						prop: 'duration',
						show: true,
					},
					{
						label: '备注',
						align: 'left',
						width: 'auto',
						prop: 'remark',
						minWidth: '200',
						show: true,
					},
				],
				dataList: {
					info: [],
					page: 0,
					record: 0,
				},
				page: {
					key: '',
					pageindex: 1,
					pagesize: 20,
					order: '',
				},
				selectRows: [],
				selectRow: {},

				dialogFormVisible: false,
			}
		},
		computed: {
			...mapGetters({
				comm: 'comm/comm',
			}),
		},
		created() {
			this.fetchData()
		},
		methods: {
			handleSelect(rows) {
				this.$emit('select', rows)
			},
			onFullscreen(v) {
				this.fullscreen = v
			},
			onSelectRows(rows) {
				this.selectRows = rows
			},
			handleAdd() {
				this.selectRow = {}
				this.dialogFormVisible = true
			},
			async fetchData(page) {
				if (page) this.page = page
				this.listLoading = true
				try {
					if (this.parentData.workId) {
						const { data } = await this.fetchListpgById(this.parentData)
						this.dataList = data
					} else {
						const { data } = await this.fetchAllListpg(this.parentData)
						this.dataList = data
					}
				} finally {
					this.listLoading = false
				}
			},
			fetchListpgById({ workId }) {
				return this.$ut.api('homecarelong/care/work/project/allListpg', {
					communityId: this.comm.id,
					workId,
					...this.page,
				})
			},
			fetchAllListpg({ customerId, workDate }) {
				return this.$ut.api('homecarelong/care/work/project/customerAllLispg', {
					communityId: this.comm.id,
					customerId,
					workDate,
					...this.page,
				})
			},
		},
	}
</script>

<style lang="scss" scoped>
	.img-box {
		width: 30px;
		height: 30px;
		border-radius: 3px;
		overflow: hidden;
		img {
			width: 100%;
			height: 100%;
		}
	}
</style>
